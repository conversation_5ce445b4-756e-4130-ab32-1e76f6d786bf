import { Component, OnInit } from '@angular/core';
import { AutomatisationService } from '../../services/automatisation.service';

@Component({
  selector: 'app-automatisation-show',
  templateUrl: './automatisation-show.component.html',
  styleUrls: ['./automatisation-show.component.css']
})
export class AutomatisationShowComponent implements OnInit {
  direction = 'gmail-to-drive';
  isRunning = false;
  isSuccess = false;
  animateIn = false;
  errorMessage: string | null = null;

  constructor(private automatisationService: AutomatisationService) { }

  ngOnInit(): void {
    setTimeout(() => {
      this.animateIn = true;
    }, 10);
  }

  setDirection(dir: 'gmail-to-drive' | 'drive-to-gmail'): void {
    this.direction = dir;
  }
  
  

  handleStartAutomation(): void {
    this.isRunning = true;
    this.isSuccess = false;
    this.errorMessage = null;
    
    this.automatisationService.batchProcessGmailAttachments().subscribe({
      next: (response) => {
        this.isRunning = false;
        this.isSuccess = true;
        

        setTimeout(() => this.isSuccess = false, 3000);
      },
      error: (error) => {
        this.isRunning = false;
        this.errorMessage = error.message || 'Une erreur est survenue lors du traitement';
        
        setTimeout(() => this.errorMessage = null, 5000);
      }
    });
  }
}

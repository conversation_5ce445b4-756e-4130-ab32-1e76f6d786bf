export class User {
    public id?: number ;
    public firstName?: string ;
    public lastName?: string ;
    public email?: string ;
    public role?: string ;
    public telephone?: string ;
    public picture?: string ;
    public password?: string ;
    public confirmPassword?: string;
    public token?: string ;
    public createAt?: string ;
    public updateAt?: string ;
    public createBy?: number ;
    public updatedBy?: number ;
    public isActive?: boolean ;
    
}
 export class Login{
    public username?: string ;
    public email?: string ;
    public password?: string ;
 }
 export interface Register {
   username: string;
   email: string;
   password1: string;
   password2: string;
 }

<!-- Sidebar -->

 <aside
 class="fixed top-0 left-0 z-40 w-64 h-screen pt-14 transition-transform -translate-x-full bg-white border-r border-gray-200 md:translate-x-0 dark:bg-gray-800 dark:border-gray-700"
 aria-label="Sidenav"
 id="drawer-navigation"
>
 <div class="overflow-y-auto py-5 px-3 h-full bg-white dark:bg-gray-800">
   <form action="#" method="GET" class="md:hidden mb-2">
     <label for="sidebar-search" class="sr-only">Search</label>
     <div class="relative">
       <div
         class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none"
       >
         <svg
           class="w-5 h-5 text-gray-500 dark:text-gray-400"
           fill="currentColor"
           viewBox="0 0 20 20"
           xmlns="http://www.w3.org/2000/svg"
         >
           <path
             fill-rule="evenodd"
             clip-rule="evenodd"
             d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
           ></path>
         </svg>
       </div>
       <input
         type="text"
         name="search"
         id="sidebar-search"
         class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
         placeholder="Search"
       />
     </div>
   </form>
   <ul class="space-y-2">
     
     <li>
       <a
         routerLink="/gmail/email-list"
         routerLinkActive="bg-indigo-50 text-indigo-600 dark:bg-gray-700"
         class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group"
       >
         <svg
           aria-hidden="true"
           class="flex-shrink-0 w-6 h-6 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
           fill="currentColor"
           viewBox="0 0 20 20"
           xmlns="http://www.w3.org/2000/svg"
         >
           <path
             d="M8.707 7.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l2-2a1 1 0 00-1.414-1.414L11 7.586V3a1 1 0 10-2 0v4.586l-.293-.293z"
           ></path>
           <path
             d="M3 5a2 2 0 012-2h1a1 1 0 010 2H5v7h2l1 2h4l1-2h2V5h-1a1 1 0 110-2h1a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5z"
           ></path>
         </svg>
         <span class="flex-1 ml-3 whitespace-nowrap">Messages</span>
         <span
           class="inline-flex justify-center items-center w-5 h-5 text-xs font-semibold rounded-full text-primary-800 bg-primary-100 dark:bg-primary-200 dark:text-primary-800"
         >
           
         </span>
       </a>
     </li>

   </ul>
   <ul
     class="pt-5 mt-5 space-y-2 border-t border-gray-200 dark:border-gray-700"
   >
     <li>
       <a
         routerLink="/drive-files/drive-files-list"
         class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg transition duration-75 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white group"
       >
         <svg
           aria-hidden="true"
           class="flex-shrink-0 w-6 h-6 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
           fill="currentColor"
           viewBox="0 0 20 20"
           xmlns="http://www.w3.org/2000/svg"
         >
           <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path>
           <path
             fill-rule="evenodd"
             d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z"
             clip-rule="evenodd"
           ></path>
         </svg>
         <span class="ml-3">Documents</span>
       </a>
     </li>
     <li>
       <a
       routerLink="/automatisation/show"
         class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg transition duration-75 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white group"
       >
         <svg
           aria-hidden="true"
           class="flex-shrink-0 w-6 h-6 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
           fill="currentColor"
           viewBox="0 0 20 20"
           xmlns="http://www.w3.org/2000/svg"
         >
           <path
             d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"
           ></path>
         </svg>
         <span class="ml-3">Automisation</span>
       </a>
     </li>

   </ul>
 

 
   
   <!-- Dropdown -->

 </div>
</aside>

import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { UserService } from '../../services/user.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-profile-management',
  templateUrl: './profile-management.component.html',
  styleUrls: ['./profile-management.component.css']
})
export class ProfileManagementComponent implements OnInit, OnDestroy {
  editing = false;
  previewImageUrl: string | null = null;
  selectedFile: File | null = null;
  loading = true;
  error: string | null = null;

  // Modèle par défaut
  profile = {
    id: null,
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    position: '',
    company: '',
    location: '',
    description: '',
    avatarUrl: 'https://via.placeholder.com/150',
    username: '',
    isSuperuser: false,
    isStaff: false,
    isActive: false,
    dateJoined: '',
    lastLogin: ''
  };

  private userSubscription: Subscription = new Subscription();

  constructor(private userService: UserService) { }

  ngOnInit(): void {
    this.userSubscription = this.userService.currentUser$.subscribe(userData => {
      if (userData) {
        this.profile = userData;
        this.loading = false;
      }
    });

    this.loadUserProfile();
  }

  ngOnDestroy(): void {
    // Assurez-vous de se désabonner pour éviter les fuites de mémoire
    this.userSubscription.unsubscribe();
  }

  loadUserProfile(): void {
    this.loading = true;
    this.userService.getCurrentUserProfile().subscribe(
      (userData) => {
        if (!userData) {
          this.error = 'Impossible de charger les données du profil. Veuillez vous reconnecter.';
          this.loading = false;
          return;
        }
          this.profile = {
          id: userData.id,
          firstName: userData.first_name || '',
          lastName: userData.last_name || '',
          email: userData.email || '',
          phone: userData.telephone || '',
          position: userData.poste || '',
          company: userData.entreprise || '',
          location: userData.localisation || '',
          description: userData.description || '',
          avatarUrl: userData.picture || 'https://via.placeholder.com/150',
          username: userData.username || '',
          isSuperuser: userData.is_superuser || false,
          isStaff: userData.is_staff || false,
          isActive: userData.is_active || false,
          dateJoined: userData.date_joined || '',
          lastLogin: userData.last_login || ''
        };
  
        this.loading = false;
      },
      (error) => {
        this.error = 'Erreur lors du chargement du profil: ' + error.message;
        this.loading = false;
      }
    );
  }
  

  toggleEdit(): void {
    this.editing = !this.editing;
    if (!this.editing) {
      this.previewImageUrl = null;
      this.selectedFile = null;
    }
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      this.selectedFile = input.files[0];

      const reader = new FileReader();
      reader.onload = (e) => {
        this.previewImageUrl = e.target?.result as string;
      };
      reader.readAsDataURL(this.selectedFile);
    }
  }

  saveProfile(): void {
    const formData = new FormData();

    formData.append('first_name', this.profile.firstName);
    formData.append('last_name', this.profile.lastName);
    formData.append('description', this.profile.description);
    formData.append('telephone', this.profile.phone);
    formData.append('poste', this.profile.position);
    formData.append('entreprise', this.profile.company);
    formData.append('localisation', this.profile.location);

    

    if (this.profile.id) {
      this.userService.updateUserProfile(this.profile.id, formData).subscribe(
        (updatedUser) => {
          this.profile = {
            ...this.profile,
            firstName: updatedUser.first_name,
            lastName: updatedUser.last_name,
            phone: updatedUser.telephone,
            position: updatedUser.poste,
            company: updatedUser.entreprise,
            location: updatedUser.localisation,
            description: updatedUser.description,
          };
          this.editing = false;
        
          alert('Profil mis à jour avec succès !');
        },
        (error) => {
          alert('Erreur lors de la mise à jour du profil: ' + error.message);
        }
      );
    }
  }
}

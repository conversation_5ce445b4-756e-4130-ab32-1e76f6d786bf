import { Injectable, PLATFORM_ID, Inject } from '@angular/core';
import { Login, Register } from './auth';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, catchError, BehaviorSubject, of, throwError, from } from 'rxjs';
import { environmentProd } from '../env/envprod';
import { isPlatformBrowser } from "@angular/common";
import { switchMap, tap } from 'rxjs/operators';

export function tokenGetter(platformId: Object): string {
  if (isPlatformBrowser(platformId)) {
    const localStorageToken = localStorage.getItem('auth_token');
    if (localStorageToken) {
      return localStorageToken;
    }
    
    const cookies = document.cookie.split(';');
    const tokenCookie = cookies.find(cookie => cookie.trim().startsWith('token='));
    if (tokenCookie) {
      return decodeURIComponent(tokenCookie.split('=')[1]);
    }
  }
  return '';
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private apiUrl = environmentProd.api || 'http://localhost:8000/api';
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  private userTokenSubject = new BehaviorSubject<string | null>(null);
  private authState = new BehaviorSubject<boolean>(false);

  constructor(
    private http: HttpClient,
    @Inject(PLATFORM_ID) private platformId: Object,
  ) {
    this.initAuthState();
  }

  /**
   * Initialise l'état d'authentification au démarrage de l'application
   */
  private initAuthState(): void {
    const token = this.getToken();
    console.log('Initializing auth state. Token:', token); 
    if (token) {
      localStorage.setItem('auth_token', token);
      
      this.userTokenSubject.next(token);
      this.isAuthenticatedSubject.next(true);
      console.log("Auth service initialized with token");
    } else {
      this.isAuthenticatedSubject.next(false);
      this.userTokenSubject.next(null);
      console.log("Auth service initialized without token");
    }
  }

  private hasToken(): boolean {
    const token = this.getToken();
    return !!token;
  }

  isAuthenticated(): Observable<boolean> {
    return this.isAuthenticatedSubject.asObservable();
  }

  setAuthenticated(value: boolean): void {
    this.isAuthenticatedSubject.next(value);
  }
  sendTokenToBackend(token: string): Observable<any> {
    console.log('Sending token to backend:', token); 
    return this.http.post(`${this.apiUrl}/user/google-login/`, { token });
  }

  logout(): void {
    console.log('Logging out user'); 
    localStorage.removeItem('auth_token');
    document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    this.userTokenSubject.next(null);
    this.setAuthenticated(false);
    console.log("User logged out");
  }

  
  checkAuthentication(): boolean {
    const token = this.getToken();
    const isAuth = !!token;
    
    if (isAuth !== this.isAuthenticatedSubject.value) {
      this.setAuthenticated(isAuth);
    }
    
    return isAuth;
  }

 
  registerUser(user: Register): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/auth/registration/`, user);
  }

  
  getToken(): string {
    return tokenGetter(this.platformId);
  }

  
  findToken(token: string = this.getToken()): Observable<any> {
    console.log('Fetching user data with token:', token);
    if (!token) {
      console.error('No token available for auth request');
      return of(null);
    }

    const headers = new HttpHeaders().set('Authorization', `Token ${token}`);

    return this.http.get<any>(`${this.apiUrl}/auth/user`, { headers }).pipe(
      catchError(error => {
        console.error('Error fetching user data:', error);
        return throwError(() => error);
      })
    );
  }
 

}

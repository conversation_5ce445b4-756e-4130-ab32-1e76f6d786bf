<div class="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-blue-50 ml-[200px]">
  <div class="max-w-5xl mx-auto p-4">

    <header class="mb-6">
      <div class="flex justify-between items-center">
        <h1 class="text-3xl font-extrabold text-gray-900 flex items-center">
          <span class="bg-gradient-to-r from-indigo-600 via-purple-500 to-blue-500 bg-clip-text text-transparent drop-shadow-sm tracking-wide">
            Messagerie des emails
          </span>
        </h1>
      </div>
    </header>


    <main class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
      
      <ng-container *ngIf="isLoading">
        <div class="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-blue-50 flex items-center justify-center">
          <div class="flex flex-col items-center">
            <div class="w-16 h-16 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
            <p class="mt-4 text-indigo-700 font-medium">Chargement de vos messages...</p>
          </div>
        </div>
      </ng-container>
      
      <ng-container *ngIf="!isLoading && selectedEmail">
         
        <div class="flex flex-col h-full">

          <div class="p-4 border-b border-gray-100 bg-white flex items-center justify-between sticky top-0 z-10 shadow-sm">
            <div class="flex items-center">
              <button (click)="handleBackClick()"
                class="mr-4 p-2 rounded-full hover:bg-indigo-50 transition-colors"
                aria-label="Retour">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" 
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                  class="text-indigo-600">
                  <path d="m12 19-7-7 7-7"></path>
                  <path d="M19 12H5"></path>
                </svg>
              </button>
              <h2 class="text-lg font-medium text-gray-900 truncate max-w-md">{{ selectedEmail.subject }}</h2>
            </div>
          </div>


          <div class="p-6 overflow-auto flex-grow">
            <div class="mb-6">
              <div class="flex items-start mb-6">

                <div [class]="'rounded-full p-3 mr-4 text-white shadow-lg bg-gradient-to-br ' + getColorFromName(extractSenderName(selectedEmail.from))">
                  {{ getInitials(extractSenderName(selectedEmail.from)) }}
                </div>

                <div class="flex-1">
                  <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                    <div>
                      <div class="flex items-center">
                        <h3 class="font-semibold text-gray-900 text-lg">
                          {{ extractSenderName(selectedEmail.from) }}
                        </h3>
                      </div>
                      <p class="text-sm text-gray-600">{{ extractEmail(selectedEmail.from) }}</p>
                    </div>
                    <div class="flex items-center mt-2 sm:mt-0 text-sm text-gray-500">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" 
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                        class="text-gray-400 mr-1">
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12 6 12 12 16 14"></polyline>
                      </svg>
                      <span>{{ formatDetailedDate(selectedEmail.date) }}</span>
                    </div>
                  </div>
                  <p class="text-sm text-gray-600 mt-2">
                    À: <span class="text-gray-800 font-medium">{{ extractSenderName(selectedEmail.to) }}</span>
                    <span class="text-gray-500">&lt;{{ extractEmail(selectedEmail.to) }}&gt;</span>
                  </p>
                </div>
              </div>


              <div class="mt-6 text-gray-800 leading-relaxed prose prose-indigo max-w-none">
                <div [innerHTML]="formatEmailBody(selectedEmail.body)"></div>
              </div>


              <div *ngIf="selectedEmail.attachments && selectedEmail.attachments.length > 0"
                class="mt-8 border-t border-gray-100 pt-6">
                <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" 
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                    class="mr-2 text-gray-500">
                    <path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>
                  </svg>
                  Pièces jointes ({{ selectedEmail.attachments.length }})
                </h4>
                <div class="flex flex-wrap gap-3">
                  <div *ngFor="let attachment of selectedEmail.attachments; let i = index"
                    class="bg-gray-50 rounded-lg p-3 flex items-center text-sm border border-gray-100 hover:border-indigo-200 hover:bg-indigo-50 transition-all group shadow-sm">
                    <div class="bg-indigo-100 rounded p-2 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" 
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                        class="text-indigo-600">
                        <path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900">{{ attachment.name || 'Pièce jointe ' + (i + 1) }}</p>
                      <p class="text-xs text-gray-500">{{ attachment.size || '1.2 MB' }}</p>
                    </div>
                    <button (click)="downloadAttachment(selectedEmail.id, attachment.id)" 
                      class="ml-4 p-1 rounded-full text-gray-400 hover:text-indigo-600 hover:bg-indigo-100 opacity-0 group-hover:opacity-100 transition-all">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" 
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7 10 12 15 17 10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ng-container>

      <ng-container *ngIf="!isLoading && !selectedEmail">

        <div class="flex flex-col h-full">
          <div class="bg-gradient-to-r from-blue-100 to-indigo-100 border-b py-6 rounded-t-lg shadow-md">
            <div class="flex flex-col md:flex-row justify-center items-center gap-4 px-4 text-center">
              <div>
                <h2 class="text-3xl font-semibold text-gray-800 leading-tight">Liste des Messages</h2>
                <p class="text-gray-500 mt-2 text-sm leading-relaxed">
                </p>
              </div>
            </div>
          </div>
          <div class="flex-grow overflow-auto">
            
            <div *ngFor="let email of emails; let i = index" 
              (click)="handleEmailClick(email.id)"
              [class]="'group border-b border-gray-100 transition-all duration-200 hover:bg-indigo-50/50 cursor-pointer ' + (!email.read ? 'bg-indigo-50/30' : 'bg-white')">
              <div class="p-4 sm:px-6">
                <div class="flex items-start space-x-4">

                  <div class="flex-shrink-0">
                    <div [class]="'h-12 w-12 rounded-full flex items-center justify-center text-white font-medium shadow-md bg-gradient-to-br ' + getColorFromName(extractSenderName(email.from))">
                      {{ getInitials(extractSenderName(email.from)) }}
                    </div>
                  </div>

                  <!-- Contenu de l'email -->
                  <div class="flex-grow min-w-0">
                    <div class="flex items-center justify-between mb-1">
                      <div class="flex items-center">
                        <h3 [class]="'font-medium text-base ' + (!email.read ? 'text-gray-900 font-semibold' : 'text-gray-800')">
                          {{ extractSenderName(email.from) }}
                        </h3>
                        <div class="flex items-center ml-2 space-x-2">
                          <div *ngIf="email.starred">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="gold" 
                              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                              class="text-yellow-500 transform group-hover:scale-110 transition-transform">
                              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                            </svg>
                          </div>
                          <svg *ngIf="email.hasAttachment" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" 
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="text-gray-400 transform group-hover:rotate-12 transition-transform">
                            <path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>
                          </svg>
                        </div>
                      </div>
                      <div class="flex items-center">
                        <span [class]="'text-sm whitespace-nowrap flex items-center ' + (!email.read ? 'font-medium text-gray-900' : 'text-gray-500')">
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" 
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="mr-1 opacity-70">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                          </svg>
                          {{ formatEmailDate(email.date) }}
                        </span>
                      </div>
                    </div>

                    <h4 [class]="'text-base mb-1 truncate ' + (!email.read ? 'font-semibold text-gray-900' : 'text-gray-700')">
                      {{ email.subject }}
                    </h4>

                    <p class="text-sm text-gray-500 line-clamp-2 leading-relaxed">
                      {{ cleanSnippet(email.snippet) }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Pied de page -->
          <div class="p-4 bg-white border-t border-gray-100 flex justify-between items-center">
            <p class="text-sm text-gray-500">{{ emails.length }} messages</p>
           
          </div>
        </div>
      </ng-container>
    </main>
  </div>
</div>

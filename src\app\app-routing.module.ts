import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  { path: '', redirectTo: '/accueil/home', pathMatch: 'full' },
  { path: 'auth', loadChildren: () => import('./auth/auth.module').then(m => m.AuthModule) },
  { path: 'profile', loadChildren: () => import('./profile/profile.module').then(m => m.ProfileModule) },
  { path: 'drive-files', loadChildren: () => import('./drive-files/drive-files.module').then(m => m.DriveFilesModule) },
  { path: 'accueil', loadChildren: () => import('./accuiel/accuiel.module').then(m => m.AccuielModule) },
  {path:'gmail', loadChildren: () => import('./gmail/gmail.module').then(m => m.GmailModule)},
  {path:'automatisation', loadChildren: () => import('./automatisation/automatisation.module').then(m => m.AutomatisationModule)},
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }

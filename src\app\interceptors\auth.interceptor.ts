import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

  constructor(private router: Router) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    // Vérifier que le token est correctement récupéré
    const token = localStorage.getItem('auth_token');
    console.log('Token in interceptor:', token);

    // Si un token existe, l'ajouter à l'en-tête d'autorisation
    if (token) {
      const authReq = request.clone({
        headers: request.headers.set('Authorization', `Token ${token}`)
      });
      
      console.log('Sending authenticated request to:', request.url);
      
      return next.handle(authReq).pipe(
        catchError((error: HttpErrorResponse) => {
          // Si l'erreur est 401 (non autorisé), rediriger vers la page de connexion
          if (error.status === 401) {
            console.error('Authorization error (401)', error);
            // localStorage.removeItem('auth_token');
            this.router.navigate(['/auth/login']);
          } else if (error.status === 403) {
            console.error('Permission denied (403)', error);
          } else {
            console.error(`HTTP error ${error.status}`, error);
          }
          
          return throwError(() => error);
        })
      );
    }

    // Si pas de token, passer la requête telle quelle
    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        console.error(`HTTP error ${error.status} for unauthenticated request`, error);
        return throwError(() => error);
      })
    );
  }
}

import { Component, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { initFlowbite } from 'flowbite';
import { AuthService } from './auth/auth.service';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit {
  showMenu = true;
  isAuthenticated = false;

  constructor(private router: Router, private authService: AuthService) { }
  title = 'myapp';

  ngOnInit(): void {
    initFlowbite();
    this.authService.isAuthenticated().subscribe(isAuth => {
      this.isAuthenticated = isAuth;
    });
    
    this.router.events
      .pipe(
        filter((event): event is NavigationEnd => event instanceof NavigationEnd) 
      )
      .subscribe((event: NavigationEnd) => {
        this.updateMenuVisibility(event.urlAfterRedirects || event.url);
      });
  }

  private updateMenuVisibility(url: string): void {
    const hiddenRoutes = ['/auth/login',  '/accueil/home'];
    this.showMenu = !hiddenRoutes.includes(url);
  }
  
}
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../auth/auth.service';
import { UserService } from '../../services/user.service';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css']
})
export class SidebarComponent implements OnInit {
  isAuthenticated = false;
  userProfile: any = null;

  constructor(
    private router: Router,
    private authService: AuthService,
    private userService: UserService
  ) { }

  ngOnInit(): void {
    console.log('SidebarComponent initialized'); // Added logging

    this.authService.isAuthenticated().subscribe(isAuth => {
      this.isAuthenticated = isAuth;
      console.log('Sidebar authentication status:', isAuth); // Added logging

      if (isAuth) {
        this.loadUserProfile();
      }
    });

    // Initial check
    this.isAuthenticated = this.authService.checkAuthentication();
    console.log('Initial authentication status:', this.isAuthenticated); 

    if (this.isAuthenticated) {
      this.loadUserProfile();
    }
  }

  loadUserProfile(): void {
    console.log('Loading user profile'); 
    
     this.userService.getCurrentUserProfile().subscribe(
       profile => {
         if (profile) {
           this.userProfile = profile;
           console.log('User profile loaded:', profile); 
    
         }
       },
       error => {
         console.error('Error loading user profile:', error); 
    
       }
     );
  }

  logout(): void {
    this.authService.logout();
    this.userService.clearUserCache();
    this.router.navigate(['/auth/login']);
  }

  navigateToProfile(): void {
    this.router.navigate(['/profile/management']);
  }
}

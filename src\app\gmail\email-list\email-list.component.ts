import { Component, OnInit } from '@angular/core';
import { trigger, transition, style, animate, state } from '@angular/animations';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SafeHtml } from '@angular/platform-browser';
import { EmailService, EmailMessage } from '../../services/email.service';
import { catchError } from 'rxjs/operators';
import { from, of } from 'rxjs'; // Add 'from' to convert Promise to Observable

// Types pour les emails
interface EmailPreview {
  id: string;
  from: string;
  subject: string;
  date: string;
  snippet: string;
  read?: boolean;
  starred?: boolean;
  hasAttachment?: boolean;
}

interface EmailDetail {
  id: string;
  from: string;
  to: string;
  subject: string;
  date: string;
  body: string;
  attachments: any[];
  read?: boolean;
  starred?: boolean;
}

@Component({
  selector: 'app-email-list',
  templateUrl: './email-list.component.html',
  animations: [
    trigger('fadeAnimation', [
      state('list', style({ opacity: 1 })),
      state('detail', style({ opacity: 1 })),
      transition('list => detail', [
        style({ opacity: 0, transform: 'translateY(10px)' }),
        animate('200ms ease-out', style({ opacity: 1, transform: 'translateY(0px)' }))
      ]),
      transition('detail => list', [
        style({ opacity: 0, transform: 'translateY(-10px)' }),
        animate('200ms ease-out', style({ opacity: 1, transform: 'translateY(0px)' }))
      ])
    ])
  ]
})
export class EmailListComponent implements OnInit {
  selectedEmailId: string | null = null;
  emails: EmailPreview[] = [];
  emailDetails: Record<string, EmailDetail> = {};
  isLoading = true;
  error: string | null = null;
  
  get selectedEmail(): EmailDetail | null {
    return this.selectedEmailId ? this.emailDetails[this.selectedEmailId] : null;
  }

  constructor(
    private sanitizer: DomSanitizer,
    private emailService: EmailService
  ) { }

  ngOnInit(): void {
    this.loadEmails();
  }

  // Charger les emails depuis le service avec tentative et fallback
  loadEmails(): void {
    this.isLoading = true;
    this.error = null;
    
    // Essayer d'abord avec getAllEmails
    this.emailService.getAllEmails()
      .pipe(
        catchError(error => {
          console.log('Primary API method failed, trying fallback method...');
          // Si ça échoue, utiliser getAllEmailsWithFallback
          return this.emailService.getAllEmailsWithFetch();
        })
      )
      .subscribe(emails => {
        this.isLoading = false;
        if (emails && emails.length) {
          // Fix type issue by explicitly typing the callback parameter
          this.emails = emails.map((email: EmailMessage) => this.convertToEmailPreview(email));
        } else {
          console.warn('No emails returned from service');
        }
      });
  }

  // Convertir un EmailMessage en EmailPreview
  convertToEmailPreview(email: EmailMessage): EmailPreview {
    return {
      id: email.id,
      from: email.from || 'Unknown Sender',
      subject: email.subject || 'No Subject',
      date: email.date || new Date().toUTCString(),
      snippet: email.snippet || '',
      read: false,
      starred: false,
      hasAttachment: email.attachments ? email.attachments.length > 0 : false
    };
  }

  // Charger les détails d'un email avec tentative et fallback
  loadEmailDetails(id: string): void {
    if (this.emailDetails[id]) {
      // Si les détails sont déjà chargés, on utilise les données en cache
      this.handleEmailClick(id);
      return;
    }
    
    this.isLoading = true;
    this.error = null;
    
    // Essayer d'abord avec getEmailById
    this.emailService.getEmailById(id)
      .pipe(
        catchError(error => {
          console.log(`Primary API method failed for email ${id}, trying fallback method...`);
          // Si ça échoue, utiliser getEmailByIdWithFallback
          return this.emailService.getEmailByIdWithFetch(id);
        })
      )
      .subscribe(email => {
        this.isLoading = false;
        
        if (email) {
          this.emailDetails[id] = {
            id: email.id,
            from: email.from || 'Unknown Sender',
            to: email.to || '<EMAIL>', // Using optional chaining to safely access the property
            subject: email.subject || 'No Subject',
            date: email.date || new Date().toUTCString(),
            body: email.body || 'No content',
            attachments: email.attachments || [],
            read: true,
            starred: false
          };
          
          this.handleEmailClick(id);
        } else {
          this.error = 'Email introuvable. Il a peut-être été supprimé.';
        }
      });
  }

  // Fonctions utilitaires
  extractSenderName(from: string): string {
    const match = from.match(/^([^<]+)/);
    return match ? match[1].trim() : from;
  }

  extractEmail(emailString: string): string {
    const match = emailString.match(/<([^>]+)>/);
    return match ? match[1] : emailString;
  }

  getInitials(name: string): string {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  }

  getColorFromName(name: string): string {
    const colors = [
      "from-indigo-500 to-purple-600",
      "from-blue-500 to-indigo-600",
      "from-emerald-500 to-teal-600",
      "from-amber-500 to-orange-600",
      "from-violet-500 to-purple-600",
      "from-pink-500 to-rose-600",
      "from-cyan-500 to-blue-600",
      "from-fuchsia-500 to-pink-600",
      "from-teal-500 to-emerald-600"
    ];

    const sum = name.split("").reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[sum % colors.length];
  }

  // Fonction pour vérifier si une date est aujourd'hui
  isToday(date: Date): boolean {
    const today = new Date();
    return date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();
  }

  // Fonction pour vérifier si une date est dans la semaine courante
  isThisWeek(date: Date): boolean {
    const today = new Date();
    const firstDay = new Date(today.setDate(today.getDate() - today.getDay()));
    const lastDay = new Date(today.setDate(today.getDate() + 6));
    return date >= firstDay && date <= lastDay;
  }

  // Fonction pour formater une date selon le format français
  formatDateFr(date: Date, format: string): string {
    const days = ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'];
    const months = ['janvier', 'février', 'mars', 'avril', 'mai', 'juin', 'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'];
    
    const day = date.getDate();
    const dayOfWeek = days[date.getDay()];
    const month = months[date.getMonth()];
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    
    switch (format) {
      case 'HH:mm':
        return `${hours}:${minutes}`;
      case 'dd/MM/yyyy':
        return `${day.toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${year}`;
      case 'EEEE d MMMM yyyy à HH:mm':
        return `${dayOfWeek} ${day} ${month} ${year} à ${hours}:${minutes}`;
      default:
        return date.toLocaleDateString('fr-FR');
    }
  }

  formatEmailDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString;

      if (this.isToday(date)) {
        return this.formatDateFr(date, 'HH:mm');
      } else if (this.isThisWeek(date)) {
        return this.formatDateFr(date, 'dd/MM/yyyy');
      } else {
        return this.formatDateFr(date, 'dd/MM/yyyy');
      }
    } catch (error) {
      return dateString;
    }
  }

  formatDetailedDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString;
      return this.formatDateFr(date, 'EEEE d MMMM yyyy à HH:mm');
    } catch (error) {
      return dateString;
    }
  }

  formatEmailBody(body: string): SafeHtml {
    // Remplacer les URLs par des liens cliquables
    let formattedBody = body.replace(
      /(https?:\/\/[^\s]+)/g,
      '<a href="$1" target="_blank" class="text-indigo-600 hover:underline transition-colors">$1</a>'
    );

    // Ajouter des sauts de paragraphe
    formattedBody = formattedBody.replace(/\r\n\r\n/g, '</p><p class="mb-4">');

    // Mettre en évidence les séparateurs
    formattedBody = formattedBody.replace(/-{10,}/g, '<hr class="my-6 border-t border-gray-200" />');

    return this.sanitizer.bypassSecurityTrustHtml(`<p class="mb-4">${formattedBody}</p>`);
  }

  cleanSnippet(snippet: string): string {
    return snippet ? snippet.replace(/&#39;/g, "'").replace(/\s*͏\s*/g, "") : '';
  }

  // Gérer les événements
  handleEmailClick(id: string): void {
    if (!this.emailDetails[id]) {
      this.loadEmailDetails(id);
      return;
    }
    
    this.selectedEmailId = id;
    // Marquer l'email comme lu
    if (this.emailDetails[id]) {
      this.emailDetails[id].read = true;
    }
    if (this.emails.find(e => e.id === id)) {
      const emailIndex = this.emails.findIndex(e => e.id === id);
      if (emailIndex !== -1) {
        this.emails[emailIndex].read = true;
      }
    }
  }

  handleBackClick(): void {
    this.selectedEmailId = null;
  }

  // Gérer les erreurs et le rechargement
  retryLoading(): void {
    if (this.selectedEmailId) {
      this.loadEmailDetails(this.selectedEmailId);
    } else {
      this.loadEmails();
    }
  }

  downloadAttachment(messageId: string, attachmentId: string): void {
    this.emailService.downloadAttachment(messageId, attachmentId).subscribe({
      next: () => {
        // Display success alert only
        alert('Attachment downloaded successfully!');
      },
      error: (error) => {
        console.error('Error downloading attachment:', error);
      }
    });
  }
}

<div class="w-full max-w-lg mx-auto shadow-md border-0 overflow-hidden bg-white rounded-lg mt-20 mr-30 ml-50">
  <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border-b py-4 px-4">
    <div class="flex items-center">
      <button type="button" (click)="onCancel()" class="mr-3 hover:bg-white/50 p-2 rounded-full transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-gray-700">
          <path d="M19 12H5M12 19l-7-7 7-7"></path>
        </svg>
      </button>
      <div>
        <h3 class="text-xl font-bold text-gray-800 leading-tight">Ajouter un Document</h3>
        <p class="text-gray-500 text-base">Téléchargez votre fichier</p>
      </div>
    </div>
  </div>

  <form (ngSubmit)="handleSubmit()">
    <div class="space-y-5 pt-5 px-5">
      <div class="space-y-2">
        <label for="file-upload" class="block text-gray-700 text-base font-medium">Fichier</label>
        <div class="border-2 border-dashed border-gray-300 rounded-md p-6 text-center hover:border-blue-400 transition-colors bg-gray-50">
          <ng-container *ngIf="file; else noFile">
            <div class="flex items-center justify-center space-x-4">
              <div class="bg-blue-100 p-3 rounded-md">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 text-blue-600">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
              </div>
              <div class="text-left">
                <p class="text-sm font-medium text-gray-800 truncate max-w-[150px]">{{ file.name }}</p>
                <p class="text-xs text-gray-500">{{ (file.size / 1024).toFixed(2) }} KB</p>
                <button
                  type="button"
                  (click)="removeFile()"
                  class="text-xs text-red-600 hover:text-red-700 mt-0.5 py-0.5 px-1 border border-red-200 hover:bg-red-50 rounded transition-colors inline-flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-0.5">
                    <path d="M3 6h18"></path>
                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
                    <path d="M8 6V4a2 2 0 0 1-2-2h4a2 2 0 0 1-2 2v2"></path>
                  </svg>
                  Supprimer
                </button>
              </div>
            </div>
          </ng-container>
          <ng-template #noFile>
            <div class="space-y-2">
              <div class="bg-blue-50 p-2 rounded-full inline-flex">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 text-blue-500 mx-auto">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="17 8 12 3 7 8"></polyline>
                  <line x1="12" y1="3" x2="12" y2="15"></line>
                </svg>
              </div>
              <div class="text-sm text-gray-600">
                <label
                  for="file-upload"
                  class="relative cursor-pointer bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs shadow-sm transition-colors inline-block"
                >
                  <span class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-0.5">
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                      <polyline points="17 8 12 3 7 8"></polyline>
                      <line x1="12" y1="3" x2="12" y2="15"></line>
                    </svg>
                    Choisir un fichier
                  </span>
                  <input
                    id="file-upload"
                    type="file"
                    class="sr-only"
                    (change)="onFileChange($event)"
                  />
                </label>
                <p class="text-xs text-gray-500 mt-0.5">PNG, JPG, PDF, DOCX, XLSX</p>
              </div>
            </div>
          </ng-template>
        </div>
      </div>

      <div class="space-y-2">
        <label for="fileName" class="block text-gray-700 text-base font-medium">Nom</label>
        <input
          id="fileName"
          [(ngModel)]="fileName"
          name="fileName"
          placeholder="Nom du fichier"
          required
          class="w-full px-4 py-3 text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
        />
      </div>

      <div class="space-y-2">
        <label for="folderName" class="block text-gray-700 text-base font-medium">Dossier</label>
        <input
          id="folderName"
          [(ngModel)]="folderName"
          name="folderName"
          placeholder="Nom du dossier"
          required
          class="w-full px-4 py-3 text-base border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
        />
        <p class="text-xs text-gray-400">ID généré auto.</p>
      </div>
    </div>
    
    <div class="flex justify-between border-t bg-gray-50 p-4 mt-5">
      <button 
        type="button" 
        (click)="onCancel()" 
        class="px-4 py-3 border border-gray-300 rounded text-sm font-medium text-gray-700 bg-white hover:bg-gray-100"
      >
        Annuler
      </button>
      <button
        type="submit"
        [disabled]="isUploading || !fileName || !folderName || !file"
        class="px-4 py-3 rounded text-sm font-medium text-white bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 flex items-center"
      >
        <svg *ngIf="!isUploading" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1 h-5 w-5">
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
          <polyline points="17 8 12 3 7 8"></polyline>
          <line x1="12" y1="3" x2="12" y2="15"></line>
        </svg>
        <svg *ngIf="isUploading" class="animate-spin -ml-0.5 mr-1 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        {{ isUploading ? 'En cours...' : 'Télécharger' }}
      </button>
    </div>
  </form>
</div>

<!-- profile-management.component.html -->
<div class="container mx-auto py-8 px-4 lg:ml-64 mt-16 transition-all duration-300">
  <div class="w-full max-w-4xl mx-auto bg-white shadow-2xl rounded-lg overflow-hidden">
    <div class="grid grid-cols-2 border-b">
      <button (click)="editing = false" [class.font-bold]="!editing" class="py-3 text-center text-gray-700 hover:bg-gray-100 transition font-medium">
        Afficher Profil
      </button>
      <button (click)="editing = true" [class.font-bold]="editing" class="py-3 text-center text-gray-700 hover:bg-gray-100 transition font-medium">
        Modifier Profil
      </button>
    </div>
    
    <div *ngIf="!editing" class="p-8">
      <div class="relative">
        <button (click)="toggleEdit()" class="absolute right-6 top-6 p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition shadow-md">
          ✏️
        </button>
      </div>
      <div class="flex flex-col items-center space-y-6 sm:flex-row sm:space-y-0 sm:space-x-8">
        <img [src]="profile.avatarUrl" class="h-28 w-28 border-4 border-gray-300 rounded-full shadow-lg"/>
        <div class="text-center sm:text-left">
          <h2 class="text-3xl font-bold text-gray-800">{{ profile.firstName }} {{ profile.lastName }}</h2>
          <p class="text-lg text-gray-500">{{ profile.position }}</p>
        </div>
      </div>
      <div class="mt-8 grid md:grid-cols-2 gap-6 text-gray-700">
        <p class="flex items-center"><span class="mr-2">📧</span>{{ profile.email }}</p>
        <p class="flex items-center"><span class="mr-2">📞</span>{{ profile.phone }}</p>
        <p class="flex items-center"><span class="mr-2">🏢</span>{{ profile.company }}</p>
        <p class="flex items-center"><span class="mr-2">📍</span>{{ profile.location }}</p>
      </div>
      <p class="mt-6 text-gray-600 leading-relaxed">{{ profile.description }}</p>
    </div>
    
    <div *ngIf="editing" class="p-8">
      <h2 class="text-2xl font-bold mb-6 text-gray-800">Modifier votre profil</h2>
      <form (ngSubmit)="saveProfile()">
        
        
        <div class="grid grid-cols-2 gap-6">
          <input [(ngModel)]="profile.firstName" name="firstName" class="border p-3 rounded-lg focus:ring-2 focus:ring-blue-500" placeholder="Prénom" />
          <input [(ngModel)]="profile.lastName" name="lastName" class="border p-3 rounded-lg focus:ring-2 focus:ring-blue-500" placeholder="Nom" />
          <input [(ngModel)]="profile.phone" name="phone" class="border p-3 rounded-lg focus:ring-2 focus:ring-blue-500" placeholder="Téléphone" />
          <input [(ngModel)]="profile.position" name="position" class="border p-3 rounded-lg focus:ring-2 focus:ring-blue-500" placeholder="Poste" />
          <input [(ngModel)]="profile.company" name="company" class="border p-3 rounded-lg focus:ring-2 focus:ring-blue-500" placeholder="Entreprise" />
          <input [(ngModel)]="profile.location" name="location" class="border p-3 rounded-lg focus:ring-2 focus:ring-blue-500" placeholder="Localisation" />
        </div>
        <textarea [(ngModel)]="profile.description" name="description" class="border p-3 rounded-lg w-full mt-6 focus:ring-2 focus:ring-blue-500" rows="4" placeholder="Description"></textarea>
        <div class="flex justify-between mt-6">
          <button type="button" (click)="toggleEdit()" class="px-6 py-3 bg-gray-200 rounded-lg hover:bg-gray-300 transition">Annuler</button>
          <button type="submit" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">Enregistrer</button>
        </div>
      </form>
    </div>
  </div>
</div>

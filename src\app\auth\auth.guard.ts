import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router, UrlTree } from '@angular/router';
import { Inject } from '@angular/core';
import { PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { tokenGetter } from './auth.service'; // import the tokenGetter function
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: object
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    const token = tokenGetter(this.platformId);
    console.log('AuthGuard checking token:', token); 

    if (token.length > 0) {
      console.log('Access granted'); 
      return true;
    }

    console.log('Access denied. Redirecting to login'); 
    this.router.navigate(['/auth/login']);
    return false;
  }
}


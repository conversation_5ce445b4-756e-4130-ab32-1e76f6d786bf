<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Ajout du lien vers Font Awesome -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
</head>
<body>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">

    <header class="border-b border-slate-200 dark:border-slate-800 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm fixed top-0 left-0 right-0 z-50">
      <div class="container mx-auto px-4 py-4 flex justify-between items-center">
        <div class="flex items-center space-x-2">

          <i class="fas fa-cogs text-emerald-600 dark:text-emerald-500 text-2xl"></i>
          <span class="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-emerald-600 to-teal-500 dark:from-emerald-500 dark:to-teal-400">
            AutomateAI
          </span>
        </div>
  
        <div class="flex items-center space-x-6">
          
          <button type="button" (click)="navigateToLogin()" class="px-4 py-2 rounded-md text-slate-700 dark:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors">Se connecter</button>
        </div>
      </div>
    </header>
  

    <main class="container mx-auto px-4 pt-32 pb-20">
      <div class="flex flex-col lg:flex-row items-center gap-12">

        <div class="lg:w-1/2 space-y-8">
          <div class="inline-flex items-center rounded-full px-3 py-1 text-sm bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400 mb-4">
            <i class="fas fa-lightbulb text-emerald-600 dark:text-emerald-400 mr-2"></i>
            <span>Propulsé par l'intelligence artificielle</span>
          </div>
  
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-900 dark:text-white leading-tight">
            Plateforme d'automatisation des tâches pilotée par l'IA
          </h1>
  
          <p class="text-lg text-slate-600 dark:text-slate-300 max-w-xl">
            Notre solution intelligente extrait automatiquement les pièces jointes de vos emails Gmail, les classe
            selon leur type (usine, e-mail, etc.) grâce à l'IA, et les organise dans Google Drive.
          </p>
  
          <div class="flex flex-col sm:flex-row gap-4 pt-4">
            <button type="button" (click)="navigateToLogin()" class="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-6 rounded-lg shadow-lg shadow-emerald-600/20 hover:shadow-emerald-600/30 transition-all inline-flex items-center">
              <span>Se connecter</span>
              <i class="fas fa-sign-in-alt ml-2"></i>
            </button>
            
          </div>
  
          <div class="flex items-center space-x-4 pt-6">
            <div class="flex -space-x-2">
              <div class="w-8 h-8 rounded-full bg-emerald-400 flex items-center justify-center text-white text-xs">
                JD
              </div>
              <div class="w-8 h-8 rounded-full bg-teal-400 flex items-center justify-center text-white text-xs">
                ML
              </div>
              <div class="w-8 h-8 rounded-full bg-cyan-400 flex items-center justify-center text-white text-xs">
                AR
              </div>
            </div>
            <p class="text-sm text-slate-600 dark:text-slate-400">
              Utilisé par <span class="font-medium text-slate-900 dark:text-white">2,500+</span> professionnels
            </p>
          </div>
        </div>
  
        <div class="lg:w-1/2 relative">
          <div class="relative rounded-2xl overflow-hidden shadow-2xl shadow-emerald-600/10 border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800">
            <div class="absolute inset-0 bg-gradient-to-br from-emerald-50 to-cyan-50 dark:from-emerald-900/20 dark:to-cyan-900/20 opacity-50"></div>
  

            <div class="relative p-6 md:p-8">
              <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-2">
                  <div class="w-3 h-3 rounded-full bg-red-400"></div>
                  <div class="w-3 h-3 rounded-full bg-yellow-400"></div>
                  <div class="w-3 h-3 rounded-full bg-green-400"></div>
                </div>
                <div class="flex items-center space-x-2">
                  <div class="w-24 h-6 rounded-md bg-slate-200 dark:bg-slate-700"></div>
                  <div class="w-6 h-6 rounded-md bg-slate-200 dark:bg-slate-700"></div>
                </div>
              </div>
  

              <div class="mb-6 p-4 bg-slate-100 dark:bg-slate-700 rounded-lg">
                <div class="flex flex-col md:flex-row items-center justify-between gap-4">

                  <div class="flex flex-col items-center">
                    <div class="w-16 h-16 rounded-lg bg-red-100 dark:bg-red-900/30 flex items-center justify-center mb-2">
                      <i class="fas fa-envelope text-red-600 dark:text-red-400 text-3xl"></i>
                    </div>
                    <span class="text-sm font-medium text-slate-700 dark:text-slate-300">Gmail</span>
                  </div>
  

                  <div class="hidden md:block text-slate-400">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                      <path d="M5 12h14"></path>
                      <path d="m12 5 7 7-7 7"></path>
                    </svg>
                  </div>
                  <div class="block md:hidden text-slate-400 -rotate-90">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                      <path d="M5 12h14"></path>
                      <path d="m12 5 7 7-7 7"></path>
                    </svg>
                  </div>
  

                  <div class="flex flex-col items-center">
                    <div class="w-16 h-16 rounded-lg bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center mb-2">
                      <i class="fas fa-brain text-emerald-600 dark:text-emerald-400 text-3xl"></i>
                    </div>
                    <span class="text-sm font-medium text-slate-700 dark:text-slate-300">Classification IA</span>
                  </div>
  

                  <div class="hidden md:block text-slate-400">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                      <path d="M5 12h14"></path>
                      <path d="m12 5 7 7-7 7"></path>
                    </svg>
                  </div>
                  <div class="block md:hidden text-slate-400 -rotate-90">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                      <path d="M5 12h14"></path>
                      <path d="m12 5 7 7-7 7"></path>
                    </svg>
                  </div>
  

                  <div class="flex flex-col items-center">
                    <div class="w-16 h-16 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mb-2">
                      <i class="fas fa-cloud text-blue-600 dark:text-blue-400 text-3xl"></i>
                    </div>
                    <span class="text-sm font-medium text-slate-700 dark:text-slate-300">Google Drive</span>
                  </div>
                </div>
              </div>
  

              <div class="space-y-3">
                <div class="flex items-center space-x-3 p-3 bg-slate-50 dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700">
                  <div class="w-8 h-8 rounded bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-red-600 dark:text-red-400">
                      <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                      <polyline points="14 2 14 8 20 8"></polyline>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="text-sm font-medium text-slate-700 dark:text-slate-300">rapport_usine.pdf</div>
                    <div class="text-xs text-slate-500">Classé comme: Document d'usine</div>
                  </div>
                  <div class="w-20 h-8 rounded bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center text-xs font-medium text-emerald-700 dark:text-emerald-400">
                    Classé
                  </div>
                </div>
  
                <div class="flex items-center space-x-3 p-3 bg-slate-50 dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700">
                  <div class="w-8 h-8 rounded bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-blue-600 dark:text-blue-400">
                      <rect x="2" y="4" width="20" height="16" rx="2"></rect>
                      <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="text-sm font-medium text-slate-700 dark:text-slate-300">message_client.eml</div>
                    <div class="text-xs text-slate-500">Classé comme: E-mail</div>
                  </div>
                  <div class="w-20 h-8 rounded bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center text-xs font-medium text-emerald-700 dark:text-emerald-400">
                    Classé
                  </div>
                </div>
  
                <div class="flex items-center space-x-3 p-3 bg-slate-50 dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700">
                  <div class="w-8 h-8 rounded bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-amber-600 dark:text-amber-400">
                      <path d="M22 20V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2h16a2 2 0 0 0 2-2Z"></path>
                      <path d="M2 10h20"></path>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="text-sm font-medium text-slate-700 dark:text-slate-300">
                      facture_fournisseur.xlsx
                    </div>
                    <div class="text-xs text-slate-500">Classé comme: Document financier</div>
                  </div>
                  <div class="w-20 h-8 rounded bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center text-xs font-medium text-emerald-700 dark:text-emerald-400">
                    Classé
                  </div>
                </div>
              </div>
            </div>
          </div>
  

          <div class="absolute -top-6 -right-6 w-24 h-24 rounded-full bg-gradient-to-br from-emerald-400 to-teal-300 blur-2xl opacity-20"></div>
          <div class="absolute -bottom-10 -left-10 w-32 h-32 rounded-full bg-gradient-to-br from-cyan-400 to-blue-300 blur-2xl opacity-20"></div>
        </div>
      </div>
    </main>
  

    <section class="bg-white dark:bg-slate-800 py-20 border-t border-slate-200 dark:border-slate-700">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16">
          <h2 class="text-3xl font-bold text-slate-900 dark:text-white mb-4">Comment ça fonctionne</h2>
          <p class="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
            Notre plateforme simplifie la gestion de vos pièces jointes en trois étapes simples
          </p>
        </div>
  
        <div class="grid md:grid-cols-3 gap-8">

          <div class="bg-slate-50 dark:bg-slate-900 p-6 rounded-xl border border-slate-200 dark:border-slate-700 hover:shadow-lg transition-shadow">
            <div class="w-12 h-12 rounded-lg bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center mb-4">
              <i class="fas fa-paperclip text-emerald-600 dark:text-emerald-400 text-2xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-slate-900 dark:text-white mb-2">Extraction des pièces jointes</h3>
            <p class="text-slate-600 dark:text-slate-400">Notre système se connecte à votre Gmail et extrait automatiquement toutes les pièces jointes de vos emails</p>
          </div>
  

          <div class="bg-slate-50 dark:bg-slate-900 p-6 rounded-xl border border-slate-200 dark:border-slate-700 hover:shadow-lg transition-shadow">
            <div class="w-12 h-12 rounded-lg bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center mb-4">
              <i class="fas fa-brain text-emerald-600 dark:text-emerald-400 text-2xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-slate-900 dark:text-white mb-2">Classification par IA</h3>
            <p class="text-slate-600 dark:text-slate-400">Notre IA analyse et classe chaque document selon son type (usine, e-mail, facture, etc.)</p>
          </div>
  

          <div class="bg-slate-50 dark:bg-slate-900 p-6 rounded-xl border border-slate-200 dark:border-slate-700 hover:shadow-lg transition-shadow">
            <div class="w-12 h-12 rounded-lg bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center mb-4">
              <i class="fas fa-folder-open text-emerald-600 dark:text-emerald-400 text-2xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-slate-900 dark:text-white mb-2">Organisation dans Drive</h3>
            <p class="text-slate-600 dark:text-slate-400">Les fichiers sont automatiquement enregistrés dans les dossiers appropriés de votre Google Drive</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</body>
</html>

import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpEventType, HttpHeaders } from '@angular/common/http';
import { Observable, throwError, of, from } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class DriveService {
  private apiUrl = 'https://lucky-wondrous-ox.ngrok-free.app';

  constructor(private http: HttpClient) {}

  listFiles(): Observable<any> {
    return this.http.get(`${this.apiUrl}/drive/list/database/`, { 
      responseType: 'json', 
      withCredentials: true  
    }).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 0) {
          return throwError(() => new Error('No internet connection.'));
        }
  
        if (error.status === 403 && error.error.includes('ngrok')) {
          return throwError(() => new Error('Ngrok tunnel requires authentication. Please open the API URL directly in a browser tab first to authorize access.'));
        }
  
        return throwError(() => new Error(`Error: ${error.message}`));
      })
    );
  }
  

  listFilesWithFetch(): Promise<any> {
    return fetch(`${this.apiUrl}/drive/list/database/`, {
      method: 'GET',
      credentials: 'include' // Include credentials for cross-origin requests
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      return response.text().then(text => {
        try {
          return JSON.parse(text);
        } catch (e) {
          console.error('Error parsing JSON:', e);
          console.log('Raw response text:', text);
          throw new Error('Invalid JSON response');
        }
      });
    });
  }

  deleteFileWithFetch(fileId: string): Promise<any> {
    // Complete the URL with the base URL
    const url = `${this.apiUrl}/drive/file/delete/${fileId}/`;
    return fetch(url, {
      method: 'DELETE',
      credentials: 'include' // Include credentials for cross-origin requests
    }).then(response => response.json());
  }
  

  getFile(fileId: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/file/${fileId}/`);
  }

  deleteFile(fileId: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/drive/file/delete/${fileId}/`, {
      withCredentials: true  
    });
    
  }
  

  

  getFolder(folderId: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/folder/${folderId}/`);
  }

  openFolder(folderId: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/folder/open/${folderId}/`);
  }

  uploadFile(file: File, fileName: string, folderName: string): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('file_name', fileName);
    formData.append('folder_name', folderName);

    return this.http.post(`${this.apiUrl}/drive/upload/`, formData, {
      withCredentials: true,
      reportProgress: true,
      observe: 'events'
    }).pipe(
      map(event => {
        switch (event.type) {
          case HttpEventType.UploadProgress:
            if (event.total) {
              const progress = Math.round(100 * event.loaded / event.total);
              return { status: 'progress', progress };
            }
            return { status: 'progress', progress: 0 };
          case HttpEventType.Response:
            return event.body;
          default:
            return { status: 'event', type: event.type };
        }
      }),
      catchError(error => {
        console.error('Error during file upload with HttpClient:', error);
        
        if (error.status === 405) {
          console.log('Trying alternative endpoint for upload...');
          return this.uploadFileFallback(file, fileName, folderName);
        }
        
        return throwError(() => error);
      })
    );
  }

  private uploadFileFallback(file: File, fileName: string, folderName: string): Observable<any> {
    console.log('Using uploadFileFallback method with fetch API');
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('fileName', fileName);
    formData.append('folderName', folderName);
    
    return from(
      fetch(`${this.apiUrl}/drive/file/upload/`, {
        method: 'POST',
        body: formData,
        credentials: 'include'
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
    ).pipe(
      map(response => {
        return {
          status: 'success',
          file_id: response.file_id || '',
          webViewLink: response.webViewLink || '',
          folder_id: response.folder_id || '',
          ...response
        };
      }),
      catchError(error => {
        console.error('Error in uploadFileFallback:', error);
        return throwError(() => new Error(`File upload failed: ${error.message}`));
      })
    );
  }


  sendEmail(emailData: { to: string; subject: string; body: string; drive_file_ids: string[] }): Observable<any> {
    return this.http.post(`${this.apiUrl}/gmail/send-with-drive-attachment/`, emailData, {
      withCredentials: true
    });
  }
  
}

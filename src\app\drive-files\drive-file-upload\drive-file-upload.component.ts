import { Component, EventEmitter, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { DriveService } from '../../services/drive.service';

export interface Document {
  id: string;
  fileName: string;
  fileLink: string;
  folderName: string;
  folderLink: string;
  folderId: string;
  uploadedAt: string;
}

@Component({
  selector: 'app-drive-file-upload',
  templateUrl: './drive-file-upload.component.html',
  styleUrls: ['./drive-file-upload.component.css'],
  standalone: false
})
export class DriveFileUploadComponent {
  @Output() cancel = new EventEmitter<void>();
  @Output() uploadComplete = new EventEmitter<Document>();

  fileName: string = '';
  folderName: string = '';
  file: File | null = null;
  isUploading: boolean = false;
  errorMessage: string = '';
  uploadProgress: number = 0;

  constructor(
    private router: Router,
    private driveService: DriveService
  ) {}

  onCancel(): void {
    this.router.navigate(['/drive-files/drive-files-list']);
  }

  onFileChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length) {
      this.file = input.files[0];
      if (!this.fileName) {
        this.fileName = this.file.name;
      }

      const validTypes = ['image/png', 'image/jpeg', 'application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
      if (!validTypes.includes(this.file.type)) {
        this.errorMessage = 'Fichier invalide. Veuillez télécharger un fichier PNG, JPG, PDF, DOCX ou XLSX.';
        this.file = null;
      }
    }
  }

  removeFile(): void {
    this.file = null;
  }

  handleSubmit(): void {
    if (!this.fileName || !this.folderName || !this.file) {
      this.errorMessage = 'Veuillez remplir tous les champs obligatoires';
      return;
    }

    this.isUploading = true;
    this.errorMessage = '';
    
    this.driveService.uploadFile(this.file, this.fileName, this.folderName)
      .subscribe({
        next: (response) => {
          if (response && response.status === 'progress') {
            this.uploadProgress = response.progress;
            return;
          }
          
          const newDocument: Document = {
            id: response.file_id || `doc-${Date.now()}`,
            fileName: this.fileName,
            fileLink: response.webViewLink || '',
            folderName: this.folderName,
            folderLink: `https://drive.google.com/drive/folders/${response.folder_id || 'Root'}`,
            folderId: response.folder_id || 'Root',
            uploadedAt: new Date().toISOString()
          };

          this.uploadComplete.emit(newDocument);
          this.isUploading = false;
          this.driveService.listFiles().subscribe({
            next: (response) => {
              console.log('Files listed successfully:', response);
            }
          });
          this.router.navigate(['/drive-files/drive-files-list']);
        },
        error: (error) => {
          console.error('Erreur lors du téléchargement du fichier:', error);
          this.errorMessage = error.error?.error || 'Une erreur est survenue lors du téléchargement du fichier';
          this.isUploading = false;
        }
      });
  }
}

import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DriveFilesListComponent } from './drive-files-list/drive-files-list.component';
import { DriveFileUploadComponent } from './drive-file-upload/drive-file-upload.component';

const routes: Routes = [
  {path:'drive-files-list',component:DriveFilesListComponent},
  {path:'drive-file-upload',component:DriveFileUploadComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DriveFilesRoutingModule { }

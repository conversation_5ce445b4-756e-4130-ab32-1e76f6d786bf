<div class="container mx-auto p-6 bg-white shadow-lg rounded-lg lg:ml-65 lg:mr-12 mt-16 mb-6 max-w-6xl">
  <ng-container *ngIf="loading">
    <div class="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-blue-50 flex items-center justify-center">
      <div class="flex flex-col items-center">
        <div class="w-16 h-16 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-indigo-700 font-medium">Chargement de vos documents...</p>
      </div>
    </div>
  </ng-container>
  
  <ng-container *ngIf="!loading">
    <div class="bg-gradient-to-r from-blue-100 to-indigo-100 border-b py-6 rounded-t-lg shadow-md">
      <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 px-4">
        <div>
          <h2 class="text-3xl font-semibold text-gray-800 leading-tight">Liste des Documents</h2>
          <p class="text-gray-500 mt-2 text-sm leading-relaxed">
          </p>
         
  
   
        </div>
         <div class="flex justify-end mt-4"> 
          <button
          (click)="openEmailModal()"
          class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-full shadow-md transition-all transform hover:scale-105 hover:shadow-lg"
        >
          Envoyer par Email
        </button>
          <button 
          (click)="navigateToUpload()"
          class="bg-gradient-to-r m-1 from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-6 py-3 rounded-full shadow-md transition-all transform hover:scale-105 hover:shadow-lg flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2a10 10 0 100 20 10 10 0 000-20zm1 10h4v2h-4v4h-2v-4H7v-2h4V8h2v4z" />
          </svg>
          <span class="text-base font-semibold">Ajouter un Document</span>
        </button>
        </div>
      </div>
    </div>
    <div class="overflow-x-auto">
      <table class="table-auto w-full border-collapse border border-gray-300 mt-3 rounded-lg shadow-sm text-sm">
        <thead>
          <tr class="bg-gray-100">
            <th class="py-3 px-4 text-gray-800 font-semibold text-left">
              <input type="checkbox" (change)="toggleSelectAll($event)" />
            </th>
            <th class="py-3 px-4 text-gray-800 font-semibold text-left">Nom du Fichier</th>
            <th class="py-3 px-4 text-gray-800 font-semibold text-left">Lien du Fichier</th>
            <th class="py-3 px-4 text-gray-800 font-semibold text-left">Nom du Dossier</th>
            <th class="py-3 px-4 text-gray-800 font-semibold text-left">Lien du Dossier</th>
            <th class="py-3 px-4 text-gray-800 font-semibold text-left">Date d'Upload</th>
            <th class="py-3 px-4 text-gray-800 font-semibold text-right">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="documents.length === 0">
            <td colspan="6" class="text-center py-16 text-gray-500">
              <div class="flex flex-col items-center justify-center space-y-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-3-3v6m-7 4h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <p class="text-lg font-medium">Aucun document trouvé</p>
                <p class="text-sm text-gray-500">Ajoutez votre premier document en cliquant sur le bouton ci-dessus</p>
              </div>
            </td>
          </tr>
          <tr *ngFor="let doc of documents" class="hover:bg-blue-50 transition-colors border-b">
            <td class="py-3 px-4">
              <input type="checkbox" [(ngModel)]="doc.selected" />
            </td>
            <td class="py-3 px-4">
              <div class="flex items-center">
                <div class="bg-blue-100 p-1.5 rounded-lg mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zM13 9V3.5L18.5 9H13z" />
                  </svg>
                </div>
                <div class="min-w-[180px]">
                  <p class="font-medium text-gray-800 text-sm truncate max-w-[240px]">{{ doc.file_name}}</p>
                  <p class="text-xs text-gray-500 mt-0.5">ID: {{ doc.file_id }}</p>
                </div>
              </div>
            </td>
            <td class="py-3 px-4">
              <a
                [href]="doc.file_link"
                target="_blank"
                class="text-blue-600 hover:text-blue-800 hover:underline flex items-center px-2 py-1 rounded-full bg-blue-50 w-fit transition-colors text-xs"
              >
                Voir
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2a10 10 0 100 20 10 10 0 000-20zm-1 14.5v-5h2v5h-2zm0-7V7h2v2.5h-2z" />
                </svg>
              </a>
            </td>
            <td class="py-3 px-4">
              <div class="flex items-center">
                <div class="bg-amber-100 p-1.5 rounded-lg mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-amber-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M10 4H4v16h16V8h-6z" />
                  </svg>
                </div>
                <span class="text-gray-800 text-sm">{{ doc.folder_name }}</span>
              </div>
            </td>
            <td class="py-3 px-4">
              <a
                [href]="doc.folder_link"
                target="_blank"
                class="text-blue-600 hover:text-blue-800 hover:underline flex items-center px-2 py-1 rounded-full bg-blue-50 w-fit transition-colors text-xs"
              >
                Voir le dossier
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 ml-1" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M10 4H4v16h16V8h-6z" />
                </svg>
              </a>
            </td>
            <td class="py-3 px-4">
              <div class="flex items-center">
                <div class="bg-gray-100 p-1.5 rounded-lg mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 3h-4.18C14.4 1.84 13.3 1 12 1s-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM12 4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm0 14c-2.21 0-4-1.79-4-4h2c0 1.1.9 2 2 2s2-.9 2-2h2c0 2.21-1.79-4-4-4z" />
                  </svg>
                </div>
                <span class="text-gray-700 text-sm">{{ formatDate(doc.upload_at) }}</span>
              </div>
            </td>
            <td class="py-3 px-4 text-right">
              <button
                (click)="deleteDocument(doc.file_id)"
                class="bg-red-100 hover:bg-red-200 text-red-600 hover:text-red-700 px-2 py-1 rounded-lg transition-colors flex items-center gap-1"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9 3v1H4v2h16V4h-5V3H9zm-3 6v10c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V9H6zm3 2h2v8H9v-8zm4 0h2v8h-2v-8z" />
                </svg>
                <span class="text-xs font-medium">Supprimer</span>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- <div class="flex justify-end mt-4">
      <button
        (click)="openEmailModal()"
        class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-full shadow-md transition-all transform hover:scale-105 hover:shadow-lg"
      >
        Envoyer par Email
      </button>
    </div> -->

    <!-- Modal -->
    <div *ngIf="isEmailModalOpen" class="modal-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
      <div class="modal bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Envoyer un Email</h2>
        <form (submit)="sendEmail($event)">
          <div class="mb-4">
            <label for="to" class="block text-sm font-medium text-gray-700">À</label>
            <input
              id="to"
              type="email"
              [(ngModel)]="emailData.to"
              name="to"
              required
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
          <div class="mb-4">
            <label for="subject" class="block text-sm font-medium text-gray-700">Sujet</label>
            <input
              id="subject"
              type="text"
              [(ngModel)]="emailData.subject"
              name="subject"
              required
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
          <div class="mb-4">
            <label for="body" class="block text-sm font-medium text-gray-700">Message</label>
            <textarea
              id="body"
              [(ngModel)]="emailData.body"
              name="body"
              rows="4"
              required
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            ></textarea>
          </div>
          <div class="mb-4">
            <h3 class="text-sm font-medium text-gray-700 mb-2">Fichiers Sélectionnés</h3>
            <ul class="list-disc pl-5 space-y-1">
              <li *ngFor="let file of selectedFiles" class="text-gray-800 text-sm">
                {{ file.file_name }}
              </li>
            </ul>
          </div>
          <div class="flex justify-end space-x-4">
            <button
              type="button"
              (click)="closeEmailModal()"
              class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md"
            >
              Annuler
            </button>
            <button
              type="submit"
              class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
            >
              Envoyer
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Confirmation Modal -->
    <div *ngIf="isConfirmationVisible" class="modal-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
      <div class="modal bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Email Envoyé</h2>
        <p class="text-gray-700 mb-4">Votre email a été envoyé avec succès.</p>
        <div class="flex justify-end">
          <button
            type="button"
            (click)="closeConfirmationModal()"
            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  </ng-container>

  <div *ngIf="isEmailSending" class="modal-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-lg p-8 flex flex-col items-center space-y-4">
      <div class="w-16 h-16 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
      <p class="text-lg font-medium text-indigo-700">Envoi de l'email en cours...</p>
      <p class="text-sm text-gray-500 text-center">
        Veuillez patienter pendant que nous envoyons votre email avec les pièces jointes sélectionnées.
      </p>
    </div>
  </div>
</div>

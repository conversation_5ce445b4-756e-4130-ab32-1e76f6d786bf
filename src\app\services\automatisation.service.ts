import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, catchError, throwError } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AutomatisationService {
  private apiUrl = 'https://noticeably-fleet-seahorse.ngrok-free.app';

  constructor(private http: HttpClient) { }

  batchProcessGmailAttachments(): Observable<any> {
    return this.http.get(`${this.apiUrl}/gmail/batch-process/`, {
      withCredentials: true  
    }).pipe(
      catchError(error => {
        console.error('Error in batch processing:', error);
        return throwError(() => new Error('Échec du traitement par lots: ' + (error.message || 'Erreur inconnue')));
      })
    );
  }
}

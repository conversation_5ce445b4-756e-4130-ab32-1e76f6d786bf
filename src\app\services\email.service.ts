import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, catchError, map, throwError } from 'rxjs';

// Interface for email message list response
export interface EmailListResponse {
  messages: EmailMessage[];
  user_email: string;
}

export interface EmailMessage {
  id: string;
  subject: string;
  from: string;
  to?: string;
  date: string;
  snippet?: string;
  body?: string;
  attachments?: any[];
}

@Injectable({
  providedIn: 'root'
})
export class EmailService {
  private baseUrl = 'https://noticeably-fleet-seahorse.ngrok-free.app/gmail';

  constructor(private http: HttpClient) { }

  /**
   * Get all email messages
   * @returns Observable with array of email messages
   */
  getAllEmails(): Observable<EmailMessage[]> {
    return this.http.get<EmailListResponse>(`${this.baseUrl}/messages/`, {
      withCredentials: true  // Include credentials in the request
    }).pipe(
      // Use map to extract the messages array from the response
      map(response => {
        if (response && Array.isArray(response.messages)) {
          return response.messages;
        } else if (response && Array.isArray(response)) {
          // If the response is already an array, return it directly
          return response;
        } else {
          console.warn('Unexpected API response format:', response);
          return [];
        }
      }),
      catchError(error => {
        console.error('Error fetching emails:', error);
        return throwError(() => new Error('Failed to fetch emails. Please try again later.'));
      })
    );
  }
  
  getAllEmailsWithFetch(): Promise<EmailMessage[]> {
    return fetch(`${this.baseUrl}/messages/`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Connection': 'keep-alive'
      },
      credentials: 'include' 
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      return response.text().then(text => {
        try {
          const json = JSON.parse(text);
          if (Array.isArray(json.messages)) {
            return json.messages;
          } else if (Array.isArray(json)) {
            return json;
          } else {
            console.warn('Unexpected API response format:', json);
            return [];
          }
        } catch (err) {
          console.error('Error parsing email list JSON:', err);
          console.log('Raw response:', text);
          throw new Error('Invalid JSON response for email list.');
        }
      });
    });
  }
  

  /**
   * Get a single email message by ID
   * @param id - Email ID to fetch
   * @returns Observable with single email message
   */
  getEmailById(id: string): Observable<EmailMessage> {
    return this.http.get<EmailMessage>(`${this.baseUrl}/messages/${id}/`,{
      withCredentials: true  // Include credentials in the request
    })
      .pipe(
        catchError(error => {
          console.error(`Error fetching email with ID ${id}:`, error);
          return throwError(() => new Error('Failed to fetch email details. Please try again later.'));
        })
      );
  }

  getEmailByIdWithFetch(id: string): Promise<EmailMessage> {
    return fetch(`${this.baseUrl}/messages/${id}/`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Connection': 'keep-alive'
      },
      credentials: 'include'
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      return response.text().then(text => {
        try {
          return JSON.parse(text);
        } catch (err) {
          console.error(`Error parsing email ${id} JSON:`, err);
          console.log('Raw response:', text);
          throw new Error('Invalid JSON response for email details.');
        }
      });
    });
  }
  
  /**
   * Download an email attachment by message ID and attachment ID
   * @param messageId - ID of the email message
   * @param attachmentId - ID of the attachment
   * @returns Observable with the attachment blob
   */
  downloadAttachment(messageId: string, attachmentId: string): Observable<Blob> {
    const url = `${this.baseUrl}/message/${messageId}/attachment/${attachmentId}/download/`;
    return this.http.get(url, {
      responseType: 'blob', // Expect a binary file (Blob) as the response
      withCredentials: true // Include credentials in the request
    }).pipe(
      catchError(error => {
        console.error(`Error downloading attachment ${attachmentId} for message ${messageId}:`, error);
        return throwError(() => new Error('Failed to download attachment. Please try again later.'));
      })
    );
  }
}

import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, of, BehaviorSubject, throwError } from 'rxjs';
import { catchError, tap, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  // URL de base pour l'API
  private apiUrl = 'https://lucky-wondrous-ox.ngrok-free.app';
  private currentUserSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  public currentUser$: Observable<any> = this.currentUserSubject.asObservable();  // Observable pour l'abonnement

  constructor(private http: HttpClient) { }

  decodeToken(token: string): any {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid token');
      }
      const decoded = atob(parts[1]);  
      const decodedData = JSON.parse(decoded);  
      console.log("detacode token =", decodedData);  
      return decodedData;
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  getCurrentUserProfile(): Observable<any> {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      console.log('No auth token found in localStorage');
      return of(null);  
    }
  
    const headers = new HttpHeaders({
      'Authorization': `Token ${token}`,
      'Content-Type': 'application/json'  
    });

    console.log('Récupération du profil utilisateur avec le token:', token);

    try {
      const decodedToken = this.decodeToken(token);
      if (!decodedToken || !decodedToken.email) {
        console.error('Invalid token or missing email in token');
        return of(null);
      }
      
      const email = decodedToken.email;

      return this.http.post(`${this.apiUrl}/user/find-user-by-email/`, { email }, { 
        headers,
        withCredentials: true 
      }).pipe(
        tap(user => {
          const transformedUser = this.transformUserData(user);
          this.currentUserSubject.next(transformedUser);  
        }),
        catchError(error => {
          console.error('Échec de la récupération du profil utilisateur:', error);
          if (error.status === 401 || error.status === 403) {
            this.handleNgrokAuth();
          }
          return of(null);  
        })
      );
    } catch (error) {
      console.error('Error processing token:', error);
      return of(null);
    }
  }

  testConnection(): Observable<any> {
    return this.http.get(`${this.apiUrl}/user/test-connection/`, {
      withCredentials: true
    }).pipe(
      catchError(error => {
        console.error('Connection test failed:', error);
        if (error.status === 401 || error.status === 403) {
          this.handleNgrokAuth();
        }
        return throwError(() => error);
      })
    );
  }

  private handleNgrokAuth(): void {
    console.log('Authentication required for ngrok tunnel');
    const ngrokUrl = this.apiUrl;
    
    // Inform user about ngrok authentication
    alert('Authentification ngrok requise. Une nouvelle fenêtre va s\'ouvrir pour vous authentifier. Veuillez l\'autoriser puis revenir à cette application.');
    
    // Open ngrok URL in a new tab
    window.open(ngrokUrl, '_blank');
  }
  
  private transformUserData(userData: any): any {
    if (!userData) return null;

    return {
      id: userData.id || '',
      firstName: userData.first_name || '',
      lastName: userData.last_name || '',
      email: userData.email || '',
      username: userData.username || '',
      description: userData.description || '',
      avatarUrl: userData.picture,
      isSuperuser: userData.is_superuser || false,
      isStaff: userData.is_staff || false,
      isActive: userData.is_active || false,
      dateJoined: userData.date_joined || '',
      lastLogin: userData.last_login || '',
      phone: userData.telephone || '',
      position: userData.poste || '',
      company: userData.entreprise || '',
      location: userData.localisation || ''
    };
  }

  // Mettre à jour les données du profil utilisateur
  updateUserProfile(userId: number, userData: any): Observable<any> {
    let formData: FormData;

    if (userData instanceof FormData) {
      formData = userData;
    } else {
      formData = new FormData();
      Object.keys(userData).forEach(key => {
        if (userData[key] !== null && userData[key] !== undefined) {
          formData.append(key, userData[key]);
        }
      });
    }

    return this.http.patch(`${this.apiUrl}/user/user-update/${userId}/`, formData, {
      withCredentials: true
    }).pipe(
      tap(updatedUser => {
        console.log('Profil mis à jour avec succès:', updatedUser);
        this.currentUserSubject.next(this.transformUserData(updatedUser));
      }),
      catchError(error => {
        if (error.status === 404) {
          console.log('Tentative de mise à jour avec le préfixe /api/');
          return this.http.patch(`${this.apiUrl}/api/user/user-update/${userId}/`, formData, {
            withCredentials: true
          }).pipe(
            tap(updatedUser => {
              console.log('Profil mis à jour avec succès avec le préfixe /api/:', updatedUser);
              this.currentUserSubject.next(this.transformUserData(updatedUser));
            }),
            catchError(this.handleError<any>('updateUserProfile with /api/ prefix', null))
          );
        }
        if (error.status === 401 || error.status === 403) {
          this.handleNgrokAuth();
        }
        return this.handleError<any>('updateUserProfile', null)(error);
      })
    );
  }

  // Effacer le cache de l'utilisateur (utile lors de la déconnexion)
  clearUserCache(): void {
    this.currentUserSubject.next(null);
    console.log('Cache utilisateur effacé');
  }

  // Gestion des erreurs génériques
  private handleError<T>(operation = 'opération', result?: T) {
    return (error: HttpErrorResponse): Observable<T> => {
      console.error(`${operation} échoué:`, error.message);
      console.error('Détails de l\'erreur:', error);
      
      // Retourner une valeur par défaut
      return of(result as T);
    };
  }
}

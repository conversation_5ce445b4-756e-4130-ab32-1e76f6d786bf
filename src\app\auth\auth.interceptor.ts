// file: c:\Users\<USER>\Desktop\Learn-angular\myapp\src\app\interceptors\auth.interceptor.ts
import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

  constructor(private router: Router) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    // Récupérer le token depuis le localStorage
    const token = localStorage.getItem('auth_token');
    console.log('Intercepting request. Token:', token); // Added logging

    // Si un token existe, l'ajouter à l'en-tête d'autorisation
    if (token) {
      const authReq = request.clone({
        headers: request.headers.set('Authorization', `Token ${token}`)
      });
      console.log('Sending authenticated request:', authReq); // Added logging
      
      return next.handle(authReq).pipe(
        catchError((error: HttpErrorResponse) => {
          console.error('HTTP error intercepted:', error); // Added logging
          // Si l'erreur est 401 (non autorisé), rediriger vers la page de connexion
          if (error.status === 401) {
            console.log('Unauthorized request, redirecting to login');
            localStorage.removeItem('auth_token');
            this.router.navigate(['/auth/login']);
          }
          
          // Pour 404, uniquement logger l'erreur sans redirection
          if (error.status === 404) {
            console.error(`404 error for URL: ${request.url}`);
          }
          
          return throwError(() => error);
        })
      );
    }

    console.log('No token found, sending request without authentication'); // Added logging
    // Si pas de token, passer la requête telle quelle
    return next.handle(request);
  }
}
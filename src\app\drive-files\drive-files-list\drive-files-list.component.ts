import { Component, OnInit } from '@angular/core';
import { DriveService } from '../../services/drive.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-drive-files-list',
  templateUrl: './drive-files-list.component.html',
  styleUrls: ['./drive-files-list.component.css']
})
export class DriveFilesListComponent {
  showUpload = false;
  documents: any[] = [];
  errorMessage: string | null = null;
  loading = true;
  isNgrokError = false;
  isEmailModalOpen = false;
  emailData = {
    to: '',
    subject: 'Test Email avec Pièces Jointes',
    body: 'Bonjour,\nVoici les pièces jointes depuis Google Drive.',
    drive_file_ids: [] as string[]
  };
  isConfirmationVisible = false;
  isEmailSending = false;

  constructor(
    private driveService: DriveService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.fetchDocuments();
  }

  fetchDocuments(): void {
    this.loading = true;
    this.errorMessage = null;
    this.isNgrokError = false;
    
    this.driveService.listFiles().subscribe({
      next: (response) => {
        console.log('Received response in component:', response);
        if (response?.files?.length) {
          this.documents = response.files;
          console.log('Documents loaded:', this.documents.length);
          this.errorMessage = null;
        } else {
          console.warn('Unexpected response structure:', response);
          this.errorMessage = 'No files found or unexpected response format.';
          this.documents = [];
          
          // Try alternative fetch method as fallback
          this.tryFetchFallback();
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error fetching documents:', error);
        
        // Check for ngrok authentication error
        if (error.message && error.message.toLowerCase().includes('ngrok')) {
          this.isNgrokError = true;
          this.errorMessage = 'Ngrok tunnel authentication required. Please open the link below in a new tab, authenticate, then return here and try again.';
        } else {
          this.errorMessage = error.message || 'An error occurred while fetching files.';
        }
        
        // Try alternative fetch method as fallback
        this.tryFetchFallback();
      }
    });
  }
  
  tryFetchFallback(): void {
    console.log('Trying fetch fallback method...');
    this.driveService.listFilesWithFetch()
      .then(response => {
        console.log('Fetch fallback successful:', response);
        if (response?.files?.length) {
          this.documents = response.files;
          this.errorMessage = null;
          this.isNgrokError = false;
          console.log('Documents loaded from fallback:', this.documents.length);
        } else {
          if (!this.isNgrokError) {
            this.errorMessage = 'No files found using fallback method.';
          }
          this.documents = [];
        }
      })
      .catch(error => {
        console.error('Fetch fallback failed:', error);
        if (!this.isNgrokError) {
          this.errorMessage = `Fallback method failed: ${error.message}`;
        }
        this.documents = [];
      })
      .finally(() => {
        this.loading = false;
      });
  }

  formatDate(dateString: string): string {
    try {
      // Check if the date string is valid
      if (!dateString || dateString === 'undefined' || dateString === 'null') {
        return 'N/A';
      }
      
      const date = new Date(dateString);
      
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }
      
      return new Intl.DateTimeFormat('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }).format(date);
    } catch (error) {
      console.error('Date formatting error:', error);
      return 'Error';
    }
  }

  deleteDocument(fileId: string): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce fichier ?')) {
      this.loading = true;
      this.errorMessage = null;
  
      this.driveService.deleteFile(fileId).subscribe({
        next: () => {
          console.log('File deleted successfully via primary method');
          // Refresh documents after successful deletion
          this.fetchDocuments();
        },
        error: (err) => {
          console.error('Erreur lors de la suppression du fichier:', err);
  
          // Si l'erreur est liée à Ngrok, afficher un message spécial
          if (err.message && err.message.toLowerCase().includes('ngrok')) {
            this.isNgrokError = true;
            this.errorMessage = 'Ngrok tunnel authentication required. Please open the link below in a new tab, authenticate, then return here and try again.';
          } else {
            this.errorMessage = err.message || 'Une erreur est survenue lors de la suppression du fichier.';
          }
  
          this.tryDeleteFallback(fileId);
          
        }
      });
    }
  }
  
  tryDeleteFallback(fileId: string): void {
    console.log('Tentative de suppression avec méthode de secours...');
  
    this.driveService.deleteFileWithFetch(fileId)
      .then(() => {
        console.log('File deleted successfully via fallback method');
        // Refresh the document list after successful deletion with fallback method
        this.fetchDocuments();
      })
      .catch((error) => {
        console.error('Échec de la suppression (méthode de secours):', error);
        this.errorMessage = `La méthode de secours a échoué: ${error.message}`;
        this.fetchDocuments();
      });
  }
  
  openNgrokAuth(): void {
    window.open('https://lucky-wondrous-ox.ngrok-free.app/drive/list/database/', '_blank');
  }

  navigateToUpload(): void {
    this.router.navigate(['/drive-files/drive-file-upload']);
  }

  get selectedFiles() {
    return this.documents.filter(doc => doc.selected);
  }

  openEmailModal() {
    this.isEmailModalOpen = true;
  }

  closeEmailModal() {
    this.isEmailModalOpen = false;
  }

  sendEmail(event: Event) {
    event.preventDefault();

    if (this.selectedFiles.length === 0) {
      alert('Veuillez sélectionner au moins un fichier pour envoyer un email.');
      return;
    }

    this.emailData.drive_file_ids = this.selectedFiles.map(doc => doc.file_id);
    this.isEmailSending = true; // Show loading spinner

    this.driveService.sendEmail(this.emailData).subscribe({
      next: (response) => {
        console.log('Email sent successfully:', response);
        this.isEmailSending = false; // Hide loading spinner
        this.closeEmailModal();
        this.isConfirmationVisible = true; // Show confirmation modal
      },
      error: (error) => {
        console.error('Error sending email:', error);
        this.isEmailSending = false; // Hide loading spinner
        alert('Erreur lors de l\'envoi de l\'email. Veuillez réessayer.');
      }
    });
  }

  closeConfirmationModal() {
    this.isConfirmationVisible = false;
  }

  toggleSelectAll(event: Event) {
    const isChecked = (event.target as HTMLInputElement).checked;
    this.documents.forEach(doc => doc.selected = isChecked);
  }
}

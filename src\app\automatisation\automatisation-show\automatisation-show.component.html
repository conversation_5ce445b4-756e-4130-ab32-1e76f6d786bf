<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 py-10 px-4 flex items-center justify-center mt-9 mb-0 ml-10 mr-5 lg:ml-60 lg:mr-0">
  <div
    class="max-w-md w-full mx-auto transition-all duration-1000 transform"
    [ngClass]="animateIn ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'"
  >

  <div class="absolute top-20 left-20 w-64 h-64 bg-purple-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"></div>
    <div class="absolute top-20 right-20 w-64 h-64 bg-yellow-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000"></div>
    <div class="absolute bottom-20 left-20 w-64 h-64 bg-pink-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-4000"></div>

    <div class="relative">
      <h1 class="text-4xl font-bold text-center mb-2 text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-violet-600">
        Automatisation
      </h1>
      <p class="text-center text-slate-500 mb-8">Connectez Gmail </p>

      <div class="shadow-2xl border-0 bg-white/90 backdrop-blur-xl rounded-2xl overflow-hidden">
        <div class="h-2 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>

        <div class="p-6 pt-8 space-y-8">

          <div class="grid grid-cols-1 gap-4">
            <div
              class="group flex items-center justify-between p-6 rounded-xl cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-105"
              [ngClass]="direction === 'gmail-to-drive' 
                ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200 shadow-lg' 
                : 'bg-white border border-slate-200 hover:border-blue-200'"
              (click)="setDirection('gmail-to-drive')"
            >
              <div class="flex items-center gap-4">
                <div
                  class="p-3 rounded-full transition-all duration-300"
                  [ngClass]="direction === 'gmail-to-drive' ? 'bg-red-100 shadow-md' : 'bg-red-50 group-hover:bg-red-100'"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                    class="h-6 w-6 transition-all duration-300"
                    [ngClass]="direction === 'gmail-to-drive' ? 'text-red-500' : 'text-red-400'"
                  >
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                </div>
                <div>
                  <span class="font-medium text-slate-700">Gmail</span>
                  <p class="text-xs text-slate-500">Pièces jointes</p>
                </div>
              </div>
              <div
                class="transition-all duration-300 transform"
                [ngClass]="direction === 'gmail-to-drive' ? 'scale-125' : 'scale-100'"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="h-5 w-5 transition-all duration-300"
                  [ngClass]="direction === 'gmail-to-drive' ? 'text-blue-500' : 'text-slate-300 group-hover:text-blue-400'"
                >
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                  <polyline points="12 5 19 12 12 19"></polyline>
                </svg>
              </div>
              <div class="flex items-center gap-4">
                <div
                  class="p-3 rounded-full transition-all duration-300"
                  [ngClass]="direction === 'gmail-to-drive' ? 'bg-green-100 shadow-md' : 'bg-green-50 group-hover:bg-green-100'"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="h-6 w-6 transition-all duration-300"
                    [ngClass]="direction === 'gmail-to-drive' ? 'text-green-500' : 'text-green-400'"
                  >
                    <line x1="22" y1="12" x2="2" y2="12"></line>
                    <path d="M5.45 5.11L2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"></path>
                    <line x1="6" y1="16" x2="6.01" y2="16"></line>
                    <line x1="10" y1="16" x2="10.01" y2="16"></line>
                  </svg>
                </div>
                <div>
                  <span class="font-medium text-slate-700">Drive</span>
                  <p class="text-xs text-slate-500">Stockage</p>
                </div>
              </div>
            </div>
          </div>


          <div class="px-5 py-4 bg-gradient-to-r from-slate-50 to-blue-50 rounded-xl text-sm text-slate-600 border border-slate-100 shadow-sm">
            <div class="flex items-start gap-3">
              <div class="mt-0.5 bg-blue-100 p-1.5 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-blue-500">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
              </div>
              <div>
                <p>Extraire et classer automatiquement les pièces jointes de Gmail vers Google Drive</p>
              </div>
            </div>
          </div>
        </div>

        <div class="p-6 pt-0">
          <button *ngIf="isSuccess"
            class="w-full py-6 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-medium flex items-center justify-center gap-2 rounded-xl shadow-lg shadow-green-200 transition-all duration-300"
            disabled
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            Automatisation réussie !
          </button>

          <button *ngIf="!isSuccess"
            class="w-full py-6 text-base font-medium bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 hover:from-blue-600 hover:via-indigo-600 hover:to-purple-600 shadow-lg shadow-blue-200/50 transition-all duration-300 text-white rounded-xl"
            (click)="handleStartAutomation()"
            [disabled]="isRunning"
          >
            <ng-container *ngIf="isRunning">
              <svg
                class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline-block"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Traitement en cours...
            </ng-container>
            <ng-container *ngIf="!isRunning">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-5 w-5 inline-block">
                <polygon points="5 3 19 12 5 21 5 3"></polygon>
              </svg>
              Lancer l'automatisation
            </ng-container>
          </button>
        </div>
      </div>


      <div class="mt-8 space-y-4">
        <h2 class="text-lg font-bold text-slate-700 flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-slate-500">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
          Récentes automatisations
        </h2>

        <div class="space-y-3">
          <div class="p-5 bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-slate-100 flex justify-between items-center hover:shadow-xl transition-all duration-300 hover:translate-y-[-2px]">
            <div class="flex items-center gap-4">
              <div class="flex items-center p-2 bg-gradient-to-r from-red-50 to-green-50 rounded-lg">
                <div class="p-1.5 rounded-full bg-red-100">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-3.5 w-3.5 text-red-500">
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-3 w-3 text-slate-400 mx-1">
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                  <polyline points="12 5 19 12 12 19"></polyline>
                </svg>
                <div class="p-1.5 rounded-full bg-green-100">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-3.5 w-3.5 text-green-500">
                    <line x1="22" y1="12" x2="2" y2="12"></line>
                    <path d="M5.45 5.11L2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"></path>
                    <line x1="6" y1="16" x2="6.01" y2="16"></line>
                    <line x1="10" y1="16" x2="10.01" y2="16"></line>
                  </svg>
                </div>
              </div>
              <div>
                <span class="text-sm font-medium text-slate-700">10 documents traités</span>
                <p class="text-xs text-slate-500">Factures et contrats</p>
              </div>
            </div>
            <div class="flex flex-col items-end">
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-green-500"></div>
                <p class="text-xs font-medium text-green-600">Terminé</p>
              </div>
              <p class="text-xs text-slate-500 mt-1">Aujourd'hui</p>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>

<style>
  @keyframes blob {
    0% {
      transform: translate(0px, 0px) scale(1);
    }
    33% {
      transform: translate(30px, -50px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
      transform: translate(0px, 0px) scale(1);
    }
  }
  .animate-blob {
    animation: blob 7s infinite;
  }
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  .animation-delay-4000 {
    animation-delay: 4s;
  }
</style>

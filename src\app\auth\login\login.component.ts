import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../auth.service';

declare const google: any;

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {

  constructor(private router: Router,  private authService: AuthService) {
   
  }

  ngOnInit(): void {
    this.authService.logout();

   
    google.accounts.id.initialize({
      client_id: '************-2q99kqegrtmmaqmvhamp0vcsvtmpn6cp.apps.googleusercontent.com', 
      callback: this.handleCredentialResponse.bind(this)
    });

    const googleButton = document.getElementById('google-signin-button');
  if (googleButton) {
    google.accounts.id.renderButton(
      googleButton,
      { theme: 'outline', size: 'large', width: '100%' }
    );
  } else {
    console.error('The Google Sign-In button container is not found!');
  }
  }

  handleCredentialResponse(response: any): void {
    const token = response.credential; 
    console.log('Google token received:', token);
  
    if (token) {
      
      localStorage.setItem('auth_token', token);
  
      
      const expireDate = new Date();
      expireDate.setHours(expireDate.getHours() + 24);
      document.cookie = `google_token=${token}; expires=${expireDate.toUTCString()}; path=/`;
  
      this.authService.setAuthenticated(true);
  
      const decodedToken = this.decodeToken(token);  
      const email = decodedToken.email;  
      const name= decodedToken.name;  
     
      this.authService.sendTokenToBackend(token).subscribe({
        next: (response) => {
          console.log('Token sent to backend:', response);
          this.login();

        },
        error: (error) => {
          console.error('Error sending token to backend:', error);
        }
      });
  
    } else {
      console.error('Google login failed: No token received');
    }
  
  }
  
  login() {
    // Après authentification réussie
    console.log('Login successful, redirecting...');
    
    // Précharger l'authentification ngrok
    fetch('https://lucky-wondrous-ox.ngrok-free.app/drive/list/database/', {
      method: 'GET',
      credentials: 'include'
    }).catch(err => console.log('Ngrok preauth attempt:', err));
    
    this.router.navigate(['/accueil/home']);
  }
  
  decodeToken(token: string): any {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid token');
    }
    const decoded = atob(parts[1]); 
    const decodedData = JSON.parse(decoded);  
    console.log("aaa",decodedData);  
    return decodedData;
  }
  

}
